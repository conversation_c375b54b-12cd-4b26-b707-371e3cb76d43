import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Status } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { PayrollRecordDto } from './dto/create-payroll-record.dto';
import { CreatePayrollUploadDto } from './dto/create-payroll-update.dto';
import { DisbursePayrollDto } from './dto/disburse-payroll.dto';

@Injectable()
export class PayrollService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findPayrollUploadById({ id }: { id: string }) {
    const payrollUpload = await this.databaseService.payrollUpload.findUnique({
      where: {
        id,
      },
    });

    return payrollUpload;
  }
  async findPayrollUpload({
    identifier,
    token,
    period,
  }: {
    identifier: string;
    token: string;
    period?: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const payrollUpload = await this.databaseService.payrollUpload.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            period,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return payrollUpload;
  }
  // Method to find a role by ID or Name
  async getPayrollUploads(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { companyId } = decodedToken;

    const payrollUploads = await this.databaseService.payrollUpload.findMany({
      where: {
        companyId,
      },
      include: {
        branch: {
          select: {
            name: true,
          },
        },
      },
    });

    return payrollUploads.map((payrollUpload) => ({
      ...payrollUpload,
      branch: payrollUpload.branch?.name.split('|')[1],
    }));
  }

  // Method to find a role by ID or Name
  async getPayrollRecordByPayrollUploadId(payrollUploadId: string) {
    const payrollRecords =
      await this.databaseService.payrollRecordUpload.findMany({
        where: {
          payrollId: payrollUploadId,
        },
      });

    return payrollRecords.map((pr) => ({
      ...pr,
      staffCode: pr.staffCode.split('|')[1],
    }));
  }

  async getPayrollBatchByPayrollUploadId(payrollUploadId: string) {
    const payrollRecords =
      await this.databaseService.payrollUploadTemp.findMany({
        where: {
          payrollUploadId,
        },
      });

    return payrollRecords;
  }

  // Method to create a new role
  async acceptPayrollAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { period, records, payDate } = JSON.parse(
          queue.data,
        ) as CreatePayrollUploadDto;

        await this.databaseService.$transaction(async (tx) => {
          const newPayrollUpload = await tx.payrollUpload.create({
            data: {
              period,
              uploadedBy: requestedBy,
              companyId,
              payDate,
              approvedBy,
            },
          });

          const payrollRecords = records.map((record) => ({
            payload: JSON.stringify(record),
            payrollUploadId: newPayrollUpload.id,
          }));

          await tx.payrollUploadTemp.createMany({
            data: payrollRecords,
            skipDuplicates: true,
          });
        });

        return true;
      }
      case ACTIONS_CONSTANT.disburse: {
        const { id } = JSON.parse(queue.data) as DisbursePayrollDto;

        await this.disbursePayroll(id);

        return true;
      }

      default:
        return false;
    }
  }

  async createPayrollRecord({ payload }: { payload: PayrollRecordDto }) {
    if (!payload.payrollId)
      throw new BadRequestException('Missing Payroll upload id');

    const payrollUploadExist = await this.findPayrollUploadById({
      id: payload.payrollId,
    });

    if (!payrollUploadExist)
      throw new NotFoundException('Payroll upload record not found');

    const staffExist = await this.databaseService.employee.findUnique({
      where: {
        staffCode: `${payrollUploadExist.companyId}|${payload.staffCode}`,
      },
    });

    if (!staffExist)
      throw new NotFoundException(
        `Staff with the code ${payload.staffCode} not found`,
      );

    await this.databaseService.payrollRecordUpload.create({
      data: {
        ...payload,
        staffCode: staffExist.staffCode,
        payrollId: payrollUploadExist.id,
        fullName: `${staffExist.firstName} ${staffExist.lastName}`,
        accountNumber: staffExist.accountNumber,
      },
    });

    return true;
  }

  async createPayroll({
    payload,
    token,
  }: {
    payload: CreatePayrollUploadDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    return await this.authorizationRequestMaker.queueRequest({
      payload: payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.PAYROLL,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async disbursePayroll(payrollUploadId: string) {
    // Validate payroll before disbursement
    await this.validatePayrollForDisbursement(payrollUploadId);

    await this.databaseService.payrollUpload.update({
      where: { id: payrollUploadId },
      data: {
        status: Status.DISBURSED,
      },
    });

    return;
  }

  private async validatePayrollForDisbursement(payrollUploadId: string) {
    const payroll = await this.databaseService.payrollUpload.findUnique({
      where: { id: payrollUploadId },
      include: {
        records: true,
        payrollUploadTemp: true,
      },
    });

    if (!payroll) {
      throw new NotFoundException(
        `Payroll upload with ID ${payrollUploadId} not found`,
      );
    }

    if (payroll.status !== Status.COMPLETED) {
      throw new BadRequestException(
        `Cannot disburse payroll in status: ${payroll.status}. Payroll must be completed before disbursement.`,
      );
    }

    // Check if there are any pending batch records
    const pendingBatchRecords = payroll.payrollUploadTemp.filter(
      (temp) => temp.status === Status.PENDING,
    );

    if (pendingBatchRecords.length > 0) {
      throw new BadRequestException(
        `Cannot disburse payroll. There are ${pendingBatchRecords.length} batch records still pending processing.`,
      );
    }

    // Check if there are any failed batch records
    const failedBatchRecords = payroll.payrollUploadTemp.filter(
      (temp) => temp.status === Status.FAILED,
    );

    if (failedBatchRecords.length > 0) {
      throw new BadRequestException(
        `Cannot disburse payroll. There are ${failedBatchRecords.length} failed batch records that need to be resolved.`,
      );
    }

    // Check if there are actual payroll records to disburse
    if (!payroll.records || payroll.records.length === 0) {
      throw new BadRequestException(
        `Cannot disburse payroll. No valid payroll records found for disbursement.`,
      );
    }

    return payroll;
  }

  async disbursePayrollRequest({
    payload,
    token,
  }: {
    payload: DisbursePayrollDto;
    token: string;
  }) {
    // Validate payroll before creating disbursement request
    const payroll = await this.validatePayrollForDisbursement(payload.id);

    const decodedToken = await this.authTokenService.decodeToken(token);

    return await this.authorizationRequestMaker.queueRequest({
      payload: { id: payroll.id, period: payroll.period },
      action: ACTIONS_CONSTANT.disburse,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.PAYROLL,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }
}
